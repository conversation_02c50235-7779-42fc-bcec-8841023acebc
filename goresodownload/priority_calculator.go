package goresodownload

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
)

// Priority calculation constants
const (
	// Default priority value used as fallback when calculation fails
	DefaultPriority = 1000

	// Priority bonuses for different property characteristics
	NoPhotoPriorityBonus      = 30000 // Highest priority for properties without photos
	ActiveStatusPriorityBonus = 10000 // High priority for active listings
	CurrentYearPriorityBonus  = 5000  // Bonus for listings from current year
	GTARegionPriorityBonus    = 500   // Bonus for Greater Toronto Area properties
	NewListingPriorityBonus   = 300   // Bonus for new listings (within 3 days)
	ResidentialPriorityBonus  = 200   // Bonus for residential/condo properties

	// Status-specific priority bonuses
	SoldStatusPriorityBonus   = 50 // Bonus for sold properties
	LeasedStatusPriorityBonus = 30 // Bonus for leased properties

	// Geographic priority bonuses
	OntarioPriorityBonus = 20 // Bonus for Ontario province properties

	// Time-based constants
	NewListingTimeWindow = 3 * 24 * time.Hour // 3 days window for new listing bonus
	MaxDaysOnMarketBonus = 30                 // Maximum days on market for bonus calculation

	// Default values for missing data
	DefaultDaysOnMarket = -1 // Default value when days on market is not available
	HoursPerDay         = 24 // Hours in a day for DOM calculation
)

// NormalizedProperty holds normalized property data across different boards
type NormalizedProperty struct {
	MlsStatus    string
	OnD          *time.Time // ListingContractDate or OriginalEntryTimestamp
	Region       string     // CountyOrParish, CityRegion, StateRegion
	PropertyType string
	Province     string // StateOrProvince
	DOM          int    // DaysOnMarket or calculated
}

// CalculatePriority calculates the priority for a property based on the CoffeeScript logic
// @param boardType - board type (TRB, BRE, DDF, EDM, CAR)
// @param existMergedProp - existing merged property record (can be nil)
// @returns priority score and error, higher score = higher priority
func CalculatePriority(boardType string, existMergedProp bson.M) (int, error) {
	priority := 0

	// Use existMergedProp as the record for normalization
	if existMergedProp == nil {
		return DefaultPriority, fmt.Errorf("existMergedProp is required")
	}

	// Normalize property data based on board type
	normalized, err := normalizePropertyData(existMergedProp, boardType)
	if err != nil {
		return DefaultPriority, fmt.Errorf("failed to normalize property data: %w", err)
	}

	// 1. No photos (phoHL or tnHL missing or empty) - highest priority
	hasPhoto := checkHasPhoto(existMergedProp)
	if !hasPhoto {
		priority += NoPhotoPriorityBonus
	}

	// 2. Active status bonus
	if isActiveStatus(normalized.MlsStatus) {
		priority += ActiveStatusPriorityBonus
	}

	// 3. Current year listing bonus
	if normalized.OnD != nil && normalized.OnD.Year() == time.Now().Year() {
		priority += CurrentYearPriorityBonus
	}

	// 4. GTA region bonus
	if isGTARegion(normalized.Region) {
		priority += GTARegionPriorityBonus
	}

	// 5. New listing bonus (within 3 days)
	if normalized.OnD != nil && time.Since(*normalized.OnD) < NewListingTimeWindow {
		priority += NewListingPriorityBonus
	}

	// 6. Residential/condo property type bonus
	if isResidentialProperty(normalized.PropertyType) {
		priority += ResidentialPriorityBonus
	}

	// 7. Status-specific bonuses
	if strings.ToLower(normalized.MlsStatus) == "sold" {
		priority += SoldStatusPriorityBonus
	} else if strings.ToLower(normalized.MlsStatus) == "leased" {
		priority += LeasedStatusPriorityBonus
	}

	// 8. Ontario province bonus
	if normalized.Province == "ON" {
		priority += OntarioPriorityBonus
	}

	// 9. Days on market bonus (0-30 days)
	if normalized.DOM >= 0 && normalized.DOM <= MaxDaysOnMarketBonus {
		priority += MaxDaysOnMarketBonus - normalized.DOM
	}

	return priority, nil
}

// normalizePropertyData converts board-specific fields to normalized format
func normalizePropertyData(record bson.M, boardType string) (*NormalizedProperty, error) {
	switch boardType {
	case "TRB":
		return normalizeTRBData(record)
	case "BRE":
		return normalizeBREData(record)
	case "DDF":
		return normalizeDDFData(record)
	case "EDM":
		return normalizeEDMData(record)
	case "CAR":
		return normalizeCARData(record)
	default:
		return nil, fmt.Errorf("unsupported board type: %s", boardType)
	}
}

// checkHasPhoto checks if the merged property has photos
// Requires both phoHL and tnHL to exist and be non-empty
func checkHasPhoto(existMergedProp bson.M) bool {
	if existMergedProp == nil {
		return false
	}

	// Check for phoHL field (photo hash list)
	hasPhoHL := false
	if phoHL, ok := existMergedProp["phoHL"]; ok && phoHL != nil {
		if phoArray, ok := phoHL.([]interface{}); ok && len(phoArray) > 0 {
			hasPhoHL = true
		}
	}

	// Check for tnHL field (thumbnail hash list)
	hasTnHL := false
	if tnHL, ok := existMergedProp["tnHL"]; ok && tnHL != nil {
		if tnArray, ok := tnHL.([]interface{}); ok && len(tnArray) > 0 {
			hasTnHL = true
		}
	}

	// Both must exist
	return hasPhoHL && hasTnHL
}

// isActiveStatus checks if the status indicates an active listing
func isActiveStatus(status string) bool {
	if status == "" {
		return false
	}
	lowerStatus := strings.ToLower(status)
	return lowerStatus == "a" || lowerStatus == "active" || lowerStatus == "new"
}

// isGTARegion checks if the region is in the Greater Toronto Area (case-insensitive)
func isGTARegion(region string) bool {
	if region == "" {
		return false
	}
	lowerRegion := strings.ToLower(region)
	return strings.Contains(lowerRegion, "toronto") ||
		strings.Contains(lowerRegion, "peel") ||
		strings.Contains(lowerRegion, "york") ||
		strings.Contains(lowerRegion, "durham") ||
		strings.Contains(lowerRegion, "halton")
}

// isResidentialProperty checks if the property type is residential or condo (case-insensitive)
func isResidentialProperty(propertyType string) bool {
	if propertyType == "" {
		return false
	}
	lowerType := strings.ToLower(propertyType)
	return strings.Contains(lowerType, "residential") ||
		strings.Contains(lowerType, "condo")
}

// parseDate attempts to parse a date from various formats
func parseDate(dateValue interface{}) *time.Time {
	if dateValue == nil {
		return nil
	}

	switch v := dateValue.(type) {
	case time.Time:
		return &v
	case string:
		// Try common date formats
		formats := []string{
			"2006-01-02",
			"2006-01-02T15:04:05Z",
			"2006-01-02T15:04:05.000Z",
			"2006-01-02T15:04:05-07:00",
		}
		for _, format := range formats {
			if t, err := time.Parse(format, v); err == nil {
				return &t
			}
		}
	}
	return nil
}

// parseInt safely converts interface{} to int
func parseInt(value interface{}) int {
	if value == nil {
		return DefaultDaysOnMarket
	}

	switch v := value.(type) {
	case int:
		return v
	case int32:
		return int(v)
	case int64:
		return int(v)
	case float64:
		return int(v)
	case string:
		if i, err := strconv.Atoi(v); err == nil {
			return i
		}
	}
	return DefaultDaysOnMarket
}

// getString safely converts interface{} to string
func getString(value interface{}) string {
	if value == nil {
		return ""
	}
	if s, ok := value.(string); ok {
		return s
	}
	return ""
}

// GetExistingMergedProperty fetches the existing merged property from database
func GetExistingMergedProperty(propID string, boardType string) (bson.M, error) {
	if propID == "" || boardType == "" {
		return nil, fmt.Errorf("propID and boardType are required")
	}

	tableName, exists := BoardMergedTable[boardType]
	if !exists {
		return nil, fmt.Errorf("unknown board type: %s", boardType)
	}

	collection := gomongo.Coll("rni", tableName)
	ctx := context.Background()

	var result bson.M
	err := collection.FindOne(ctx, bson.M{"_id": propID}).Decode(&result)
	if err != nil {
		// Document not found is not an error for priority calculation
		if err.Error() == "mongo: no documents in result" {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to fetch merged property: %w", err)
	}

	return result, nil
}

// Board-specific normalization functions

// normalizeTRBData normalizes TRB board data
func normalizeTRBData(record bson.M) (*NormalizedProperty, error) {
	normalized := &NormalizedProperty{}

	// MlsStatus -> status
	normalized.MlsStatus = getString(record["MlsStatus"])

	// ListingContractDate -> onD, OriginalEntryTimestamp -> ts
	if onD := parseDate(record["ListingContractDate"]); onD != nil {
		normalized.OnD = onD
	} else {
		normalized.OnD = parseDate(record["OriginalEntryTimestamp"])
	}

	// CountyOrParish -> region
	normalized.Region = getString(record["CountyOrParish"])

	// PropertyType -> ptype
	normalized.PropertyType = getString(record["PropertyType"])

	// StateOrProvince -> prov
	normalized.Province = getString(record["StateOrProvince"])

	// DaysOnMarket -> dom
	normalized.DOM = parseInt(record["DaysOnMarket"])

	return normalized, nil
}

// normalizeBREData normalizes BRE board data
func normalizeBREData(record bson.M) (*NormalizedProperty, error) {
	normalized := &NormalizedProperty{}

	// MlsStatus -> status
	normalized.MlsStatus = getString(record["MlsStatus"])

	// ListingContractDate -> onD
	if onD := parseDate(record["ListingContractDate"]); onD != nil {
		normalized.OnD = onD
	} else {
		normalized.OnD = parseDate(record["ts"])
	}

	// CountyOrParish -> region
	normalized.Region = getString(record["CountyOrParish"])

	// PropertyType -> ptype
	normalized.PropertyType = getString(record["PropertyType"])

	// StateOrProvince -> prov
	normalized.Province = getString(record["StateOrProvince"])

	// DOM calculated from OffMarketDate, ListingContractDate
	normalized.DOM = calculateDOMFromDates(record["ListingContractDate"], record["OffMarketDate"])

	return normalized, nil
}

// normalizeDDFData normalizes DDF board data
func normalizeDDFData(record bson.M) (*NormalizedProperty, error) {
	normalized := &NormalizedProperty{}

	// MlsStatus -> status
	normalized.MlsStatus = getString(record["MlsStatus"])

	// OriginalEntryTimestamp -> onD and ts
	normalized.OnD = parseDate(record["OriginalEntryTimestamp"])

	// CityRegion -> region (could also be cmty)
	normalized.Region = getString(record["CityRegion"])

	// PropertySubType -> ptype (needs special calculation)
	normalized.PropertyType = getString(record["PropertySubType"])

	// StateOrProvince -> prov
	normalized.Province = getString(record["StateOrProvince"])

	// DOM calculated from OriginalEntryTimestamp, ExpirationDate
	normalized.DOM = calculateDOMFromDates(record["OriginalEntryTimestamp"], record["ExpirationDate"])

	return normalized, nil
}

// normalizeEDMData normalizes EDM board data
func normalizeEDMData(record bson.M) (*NormalizedProperty, error) {
	normalized := &NormalizedProperty{}

	// MlsStatus -> status
	normalized.MlsStatus = getString(record["MlsStatus"])

	// ListingContractDate -> onD (ts field deleted in mapping)
	normalized.OnD = parseDate(record["ListingContractDate"])

	// StateRegion -> region
	normalized.Region = getString(record["StateRegion"])

	// PropertyType -> ptype
	normalized.PropertyType = getString(record["PropertyType"])

	// StateOrProvince -> prov
	normalized.Province = getString(record["StateOrProvince"])

	// CumulativeDaysOnMarket -> dom
	normalized.DOM = parseInt(record["CumulativeDaysOnMarket"])

	return normalized, nil
}

// normalizeCARData normalizes CAR board data
func normalizeCARData(record bson.M) (*NormalizedProperty, error) {
	normalized := &NormalizedProperty{}

	// MlsStatus -> status
	normalized.MlsStatus = getString(record["MlsStatus"])

	// ListingContractDate -> onD (ts field deleted in mapping)
	normalized.OnD = parseDate(record["ListingContractDate"])

	// CountyOrParish -> region
	normalized.Region = getString(record["CountyOrParish"])

	// PropertyType -> ptype
	normalized.PropertyType = getString(record["PropertyType"])

	// StateOrProvince -> prov
	normalized.Province = getString(record["StateOrProvince"])

	// DaysOnMarket -> dom
	normalized.DOM = parseInt(record["DaysOnMarket"])

	return normalized, nil
}

// Helper functions

// calculateDOMFromDates calculates days on market from start and end dates
// If endDate is nil, uses current time as end date
func calculateDOMFromDates(startDate, endDate interface{}) int {
	start := parseDate(startDate)
	if start == nil {
		return DefaultDaysOnMarket
	}

	var endTime time.Time
	end := parseDate(endDate)
	if end != nil {
		endTime = *end
	} else {
		// If no end date (like OffMarketDate), use current time
		endTime = time.Now()
	}

	duration := endTime.Sub(*start)
	days := int(duration.Hours() / HoursPerDay)

	if days < 0 {
		return DefaultDaysOnMarket
	}

	return days
}
