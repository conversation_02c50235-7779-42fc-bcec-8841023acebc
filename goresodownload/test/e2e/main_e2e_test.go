package e2e

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"syscall"
	"testing"
	"time"

	gobase "github.com/real-rm/gobase"
	gohelper "github.com/real-rm/gohelper"
	levelStore "github.com/real-rm/golevelstore"
	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
	goresodownload "github.com/real-rm/goresodownload"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var (
	gBoardType string
	dryRun     bool
	force      bool
)

// MediaKeys represents the media key values for testing
type MediaKeys struct {
	Media1            int32
	Media2            int32
	Media1Thumb       int32
	Doc1              string
	Doc2              string
	Media1_replace    int32
	Media2_replace    int32
	Media1_tn_replace int32
	Doc1_replace      string
	Doc2_replace      string
}

// TestConfig holds the configuration for board-specific tests
type TestConfig struct {
	BoardType    string
	DocumentID   string
	DocumentData bson.M
}

// setupE2ETest sets up the test environment for end-to-end tests
func setupE2ETest(t *testing.T) (*httptest.Server, func()) {
	currentDir, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current directory: %v", err)
	}
	projectRoot := filepath.Dir(filepath.Dir(currentDir))
	configPath, err := filepath.Abs(filepath.Join(projectRoot, "local.test.ini"))
	if err != nil {
		t.Fatalf("Failed to get absolute path: %v", err)
	}
	gohelper.SetRmbaseFileCfg(configPath)
	// Initialize test environment
	if err := gohelper.SetupTestEnv(gohelper.TestOptions{
		UseEnvConfig: true,
	}); err != nil {
		t.Fatalf("Failed to setup test environment: %v", err)
	}

	// Set up global variables for testing
	gBoardType = "TRB" // Default to TRB for testing
	dryRun = true      // Always run in dry run mode for tests
	force = true       // Force start for tests

	// Clean up collections before each test
	sysdataColl := gomongo.Coll("rni", "sysdata")
	if _, err := sysdataColl.DeleteMany(context.Background(), bson.M{}); err != nil {
		t.Fatalf("Failed to delete sysdata collection: %v", err)
	}
	fileServerColl := gomongo.Coll("rni", "file_server")
	if _, err := fileServerColl.DeleteMany(context.Background(), bson.M{}); err != nil {
		t.Fatalf("Failed to delete file_server collection: %v", err)
	}
	failedColl := gomongo.Coll("rni", "reso_photo_download_failed")
	if _, err := failedColl.DeleteMany(context.Background(), bson.M{}); err != nil {
		t.Fatalf("Failed to delete failed collection: %v", err)
	}

	// Clean up all board collections
	for _, boardType := range []string{"CAR", "DDF", "BRE", "EDM", "TRB"} {
		watchColl := gomongo.Coll("rni", goresodownload.BoardWatchTable[boardType])
		if _, err := watchColl.DeleteMany(context.Background(), bson.M{}); err != nil {
			t.Fatalf("Failed to delete watch collection for %s: %v", boardType, err)
		}
		mergedColl := gomongo.Coll("rni", goresodownload.BoardMergedTable[boardType])
		if _, err := mergedColl.DeleteMany(context.Background(), bson.M{}); err != nil {
			t.Fatalf("Failed to delete merged collection for %s: %v", boardType, err)
		}
	}

	// Clean up storage paths
	for _, boardType := range []string{"CAR", "DDF", "BRE", "EDM", "TRB"} {
		storagePaths, err := levelStore.GetImageDir(boardType)
		if err != nil {
			t.Fatalf("Failed to get image directories for %s: %v", boardType, err)
		}
		for _, path := range storagePaths {
			if !strings.Contains(path, "test") && !strings.Contains(path, "tmp") {
				t.Fatalf("Refusing to delete non-test path: %s", path)
			}
			if err := os.RemoveAll(path); err != nil {
				t.Fatalf("Failed to delete storage path %s: %v", path, err)
			}
			if err := os.MkdirAll(path, 0755); err != nil {
				t.Fatalf("Failed to create storage path %s: %v", path, err)
			}
		}
	}

	// Load fixtures for each test
	cfg := &FixtureConfig{
		Folder: currentDir,
	}
	if err := LoadFixtures(cfg); err != nil {
		t.Fatalf("Failed to load fixtures: %v", err)
	}

	// Get test images from current directory
	img1Path := filepath.Join(currentDir, "media", "success1.jpg")
	img2Path := filepath.Join(currentDir, "media", "success2.jpg")
	// img3Path := filepath.Join(currentDir, "error.jpg")
	img4Path := filepath.Join(currentDir, "media", "timeout.jpg")
	pdfPath := filepath.Join(currentDir, "media", "test.pdf")
	audioPath := filepath.Join(currentDir, "media", "audio.mp3")

	// Verify test images exist
	if _, err := os.Stat(img1Path); os.IsNotExist(err) {
		t.Fatalf("Test image 1 not found: %s", img1Path)
	}
	if _, err := os.Stat(img2Path); os.IsNotExist(err) {
		t.Fatalf("Test image 2 not found: %s", img2Path)
	}

	// Create mock image server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Serve local test images
		switch r.URL.Path {
		case "/success1.jpg":
			http.ServeFile(w, r, img1Path)
		case "/success2.jpg":
			http.ServeFile(w, r, img2Path)
		case "/test.pdf":
			http.ServeFile(w, r, pdfPath)
		case "/audio.mp3":
			http.ServeFile(w, r, audioPath)
		case "/error.jpg":
			w.WriteHeader(http.StatusInternalServerError)
		case "/timeout.jpg":
			time.Sleep(2 * time.Second)
			w.Header().Set("Content-Type", "image/jpeg")
			http.ServeFile(w, r, img4Path)
		default:
			w.WriteHeader(http.StatusNotFound)
		}
	}))

	// Initialize test environment
	if err := gobase.InitBase(); err != nil {
		t.Fatalf("Failed to initialize base: %v", err)
	}
	if err := gomongo.InitMongoDB(); err != nil {
		t.Fatalf("Failed to initialize MongoDB: %v", err)
	}

	return server, func() {
		server.Close()
	}
}

func runMainProgram(t *testing.T, boardType string) (*exec.Cmd, context.CancelFunc) {
	ctx, cancel := context.WithCancel(context.Background())
	wd, _ := os.Getwd()
	projectRoot := filepath.Dir(filepath.Dir(wd))
	mainDir := filepath.Join(projectRoot, "cmd", "goresodownload")

	golog.Debug("Starting main program from:", mainDir)
	// Use "go run ." to run all Go files in the directory
	cmd := exec.CommandContext(ctx, "go", "run", ".", "-board", boardType, "-dryrun", "-force", "-batchSize", "1")
	cmd.Dir = mainDir // Set working directory to the main package directory

	// Capture stdout and stderr
	// cmd.Stdout = os.Stdout
	// cmd.Stderr = os.Stderr

	// Start the process
	if err := cmd.Start(); err != nil {
		cancel()
		t.Fatalf("Failed to start main program: %v", err)
	}

	// Wait a bit to ensure program is running
	time.Sleep(2 * time.Second)

	// Check if process is still running
	if cmd.Process == nil {
		cancel()
		t.Fatalf("Main program failed to start")
	}
	golog.Debug("Main program started with PID:", cmd.Process.Pid)

	return cmd, cancel
}

func cleanupMainProcess(t *testing.T, cmd *exec.Cmd, cancel context.CancelFunc) {
	if cmd == nil || cmd.Process == nil {
		return
	}

	cancel()

	// Try graceful shutdown first
	_ = cmd.Process.Signal(syscall.SIGINT)

	done := make(chan error, 1)
	go func() {
		done <- cmd.Wait()
	}()

	select {
	case err := <-done:
		if err != nil {
			t.Logf("Process exited with error: %v", err)
		}
	case <-time.After(5 * time.Second):
		t.Logf("Timeout waiting for process to exit. Killing it.")
		_ = cmd.Process.Kill()
		<-done
	}
}

// TestCase represents a test case for board-specific tests
type TestCase struct {
	name       string
	boardType  string
	documentID string
	sidField   string
	mediaField string
	mediaKeys  MediaKeys
}

// verifyDownloadedFiles verifies that files are downloaded to the correct paths
func verifyDownloadedFiles(t *testing.T, rawOne bson.M, phoUrls primitive.A, tc TestCase, storagePaths []string, isUpdate bool) {
	// 使用GetPropTsForPath获取正确的时间戳，保证与实际文件路径计算的一致性
	tsDate, err := goresodownload.GetPropTsForPath(rawOne, gBoardType)
	require.NoError(t, err, "Failed to get PropTs for path")
	sid := rawOne[tc.sidField].(string)
	path, err := levelStore.GetFullFilePathForProp(tsDate, gBoardType, sid)
	require.NoError(t, err)
	golog.Debug("path", path)
	require.NotEmpty(t, storagePaths, "Image directory should be configured")
	imgDir := filepath.Join(storagePaths[0], path)

	keyField := "MediaKey"
	if tc.boardType == "TRB" {
		keyField = "key"
	}

	media1Int32 := levelStore.MurmurToInt32(phoUrls[0].(primitive.M)[keyField].(string))
	media1TnInt32 := levelStore.MurmurToInt32(phoUrls[0].(primitive.M)[keyField].(string) + "-t")
	media1Name, err := levelStore.Int32ToBase62(media1Int32)
	require.NoError(t, err)
	media1TnName, err := levelStore.Int32ToBase62(media1TnInt32)
	require.NoError(t, err)
	expectedPaths := []string{
		filepath.Join(imgDir, sid+"_"+media1Name+".jpg"),
		filepath.Join(imgDir, sid+"_"+media1TnName+".jpg"),
	}

	if !isUpdate {
		media2Int32 := levelStore.MurmurToInt32(phoUrls[1].(primitive.M)[keyField].(string))
		media2Name, err := levelStore.Int32ToBase62(media2Int32)
		require.NoError(t, err)
		expectedPaths = append(expectedPaths, filepath.Join(imgDir, sid+"_"+media2Name+".jpg"))
	}

	// Verify files are downloaded to correct path

	// Add document paths for TRB board
	if tc.boardType == "TRB" && !isUpdate {
		// Extract media keys for doc files
		doc1Int32 := levelStore.MurmurToInt32(phoUrls[2].(primitive.M)[keyField].(string))
		doc2Int32 := levelStore.MurmurToInt32(phoUrls[3].(primitive.M)[keyField].(string))
		doc1Name, err := levelStore.Int32ToBase62(doc1Int32)
		require.NoError(t, err)
		doc2Name, err := levelStore.Int32ToBase62(doc2Int32)
		require.NoError(t, err)

		// Add document files to expected paths
		docPaths := []string{
			filepath.Join(imgDir, sid+"_"+doc1Name+".mp3"),
			filepath.Join(imgDir, sid+"_"+doc2Name+".pdf"),
		}
		expectedPaths = append(expectedPaths, docPaths...)
	}

	for _, path := range expectedPaths {
		assert.FileExists(t, path, "File should be downloaded to: %s", path)
	}
}

func TestE2ECAR(t *testing.T) {
	testCases := []TestCase{
		{
			name:       "CAR Board Test",
			boardType:  "CAR",
			documentID: "CAR40401964",
			sidField:   "ListingId",
			mediaField: "Media",
			mediaKeys: MediaKeys{
				Media1:            levelStore.MurmurToInt32("27052b3fad0dba9b0c4a4a53c04ee1d5-m1"),
				Media2:            levelStore.MurmurToInt32("27052b3fad0dba9b0c4a4a53c04ee1d5-m2"),
				Media1Thumb:       levelStore.MurmurToInt32("27052b3fad0dba9b0c4a4a53c04ee1d5-m1-t"),
				Media1_replace:    levelStore.MurmurToInt32("27052b3fad0dba9b0c4a4a53c04ee1d5-m1_replace"),
				Media2_replace:    levelStore.MurmurToInt32("27052b3fad0dba9b0c4a4a53c04ee1d5-m2_replace"),
				Media1_tn_replace: levelStore.MurmurToInt32("27052b3fad0dba9b0c4a4a53c04ee1d5-m1_replace-t"),
			},
		},
	}

	for _, tc := range testCases {
		tc := tc // Create new variable to avoid closure issues
		t.Run(tc.name, func(t *testing.T) {
			runBoardTest(t, tc)
		})
	}
}

func TestE2EDDF(t *testing.T) {
	testCases := []TestCase{
		{
			name:       "DDF Board Test",
			boardType:  "DDF",
			documentID: "DDF25716102",
			sidField:   "ListingKey",
			mediaField: "Media",
			mediaKeys: MediaKeys{
				Media1:            levelStore.MurmurToInt32("6040231433"),
				Media2:            levelStore.MurmurToInt32("6040231441"),
				Media1Thumb:       levelStore.MurmurToInt32("6040231433-t"),
				Media1_replace:    levelStore.MurmurToInt32("6040231433_replace"),
				Media2_replace:    levelStore.MurmurToInt32("6040231441_replace"),
				Media1_tn_replace: levelStore.MurmurToInt32("6040231433_replace-t"),
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			runBoardTest(t, tc)
		})
	}
}

func TestE2EBRE(t *testing.T) {
	testCases := []TestCase{
		{
			name:       "BRE Board Test",
			boardType:  "BRE",
			documentID: "BRER2948091",
			sidField:   "ListingId",
			mediaField: "Media",
			mediaKeys: MediaKeys{
				Media1:            levelStore.MurmurToInt32("2f71c2c922224a3d08bbb49a67b45894-m1"),
				Media2:            levelStore.MurmurToInt32("2f71c2c922224a3d08bbb49a67b45894-m2"),
				Media1Thumb:       levelStore.MurmurToInt32("2f71c2c922224a3d08bbb49a67b45894-m1-t"),
				Media1_replace:    levelStore.MurmurToInt32("2f71c2c922224a3d08bbb49a67b45894-m1_replace"),
				Media2_replace:    levelStore.MurmurToInt32("2f71c2c922224a3d08bbb49a67b45894-m2_replace"),
				Media1_tn_replace: levelStore.MurmurToInt32("2f71c2c922224a3d08bbb49a67b45894-m1_replace-t"),
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			runBoardTest(t, tc)
		})
	}
}

func TestE2EEDM(t *testing.T) {
	testCases := []TestCase{
		{
			name:       "EDM Board Test",
			boardType:  "EDM",
			documentID: "EDME4330056",
			sidField:   "ListingId",
			mediaField: "Media",
			mediaKeys: MediaKeys{
				Media1:            levelStore.MurmurToInt32("282ef4e2fb8b017e3d0a0d81ea32baa8-m1"),
				Media2:            levelStore.MurmurToInt32("282ef4e2fb8b017e3d0a0d81ea32baa8-m2"),
				Media1Thumb:       levelStore.MurmurToInt32("282ef4e2fb8b017e3d0a0d81ea32baa8-m1-t"),
				Media1_replace:    levelStore.MurmurToInt32("282ef4e2fb8b017e3d0a0d81ea32baa8-m1_replace"),
				Media2_replace:    levelStore.MurmurToInt32("282ef4e2fb8b017e3d0a0d81ea32baa8-m2_replace"),
				Media1_tn_replace: levelStore.MurmurToInt32("282ef4e2fb8b017e3d0a0d81ea32baa8-m1_replace-t"),
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			runBoardTest(t, tc)
		})
	}
}

func TestE2ETRB(t *testing.T) {
	testCases := []TestCase{
		{
			name:       "TRB Board Test",
			boardType:  "TRB",
			documentID: "TRBX12213558",
			sidField:   "ListingKey",
			mediaField: "media",
			mediaKeys: MediaKeys{
				Media1:            levelStore.MurmurToInt32("15d621c7-4fd4-4b93-b746-c64bcdd53dde-nw"),
				Media2:            levelStore.MurmurToInt32("f45f2f83-b822-421b-9264-d5c4d542edd3"),
				Media1Thumb:       levelStore.MurmurToInt32("15d621c7-4fd4-4b93-b746-c64bcdd53dde-nw-t"),
				Doc1:              fmt.Sprintf("%d.mp3", levelStore.MurmurToInt32("trreb/c11897846/audio")),
				Doc2:              fmt.Sprintf("%d.pdf", levelStore.MurmurToInt32("a3c5f9ba-0e09-4125-b773-30135ddc4fae")),
				Media1_replace:    levelStore.MurmurToInt32("15d621c7-4fd4-4b93-b746-c64bcdd53dde-nw_replace"),
				Media2_replace:    levelStore.MurmurToInt32("f45f2f83-b822-421b-9264-d5c4d542edd3_replace"),
				Media1_tn_replace: levelStore.MurmurToInt32("15d621c7-4fd4-4b93-b746-c64bcdd53dde-nw_replace-t"),
				Doc1_replace:      fmt.Sprintf("%d.mp3", levelStore.MurmurToInt32("trreb/c11897846/audio_replace")),
				Doc2_replace:      fmt.Sprintf("%d.pdf", levelStore.MurmurToInt32("a3c5f9ba-0e09-4125-b773-30135ddc4fae_replace")),
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			runBoardTest(t, tc)
			fmt.Println("TestE2ETRB")
		})
	}
}

// runBoardTest runs the test for a specific board type
func runBoardTest(t *testing.T, tc TestCase) {
	server, cleanup := setupE2ETest(t)
	// Move defer cleanup() to the end to ensure server stays up during the entire test

	// Set board type
	gBoardType = tc.boardType
	storagePaths, err := levelStore.GetImageDir(gBoardType)
	if err != nil {
		t.Fatalf("Failed to get image directories for %s: %v", gBoardType, err)
	}
	fmt.Println("storagePaths", storagePaths)
	// delete the storage paths
	for _, path := range storagePaths {
		if err := os.RemoveAll(path); err != nil {
			t.Fatalf("Failed to delete storage path: %v", err)
		}
	}

	// Start the main program
	cmd, cancel := runMainProgram(t, tc.boardType)
	defer func() {
		cleanupMainProcess(t, cmd, cancel)
		cleanup() // Move cleanup here to ensure server stays up during the test
	}()

	// get the raw one
	watchColl := gomongo.Coll("rni", goresodownload.BoardWatchTable[gBoardType])
	var rawOne bson.M
	if err := watchColl.FindOne(context.Background(), bson.M{"_id": tc.documentID}).Decode(&rawOne); err != nil {
		t.Fatalf("Failed to find document: %v", err)
	}
	golog.Debug("Found raw document:", rawOne["_id"])
	fmt.Println("Found raw document:", rawOne["_id"])

	// delete the raw one
	if _, err := watchColl.DeleteOne(context.Background(), bson.M{"_id": tc.documentID}); err != nil {
		t.Fatalf("Failed to delete document: %v", err)
	}
	golog.Debug("Deleted existing document")

	// merged collection
	mergedColl := gomongo.Coll("rni", goresodownload.BoardMergedTable[gBoardType])

	phoField := "phoLH"
	tnField := "tnLH"
	docField := "docLH"
	urlField := "MediaURL"
	keyField := "MediaKey"
	if tc.boardType == "TRB" {
		urlField = "url"
		keyField = "key"
	}

	// Test Insert
	t.Run("Insert", func(t *testing.T) {
		// Check if Media field exists and is an array
		mediaValue, exists := rawOne[tc.mediaField]
		if !exists {
			t.Fatalf("Media field not found in document")
		}
		phoUrls, ok := mediaValue.(primitive.A)
		if !ok {
			t.Fatalf("Media field is not an array")
		}
		if len(phoUrls) < 2 {
			t.Fatalf("Media array should have at least 2 elements")
		}

		phoUrls[0].(primitive.M)[urlField] = server.URL + "/success1.jpg"
		phoUrls[1].(primitive.M)[urlField] = server.URL + "/success2.jpg"
		if tc.boardType == "TRB" {
			phoUrls[2].(primitive.M)[urlField] = server.URL + "/audio.mp3"
			phoUrls[3].(primitive.M)[urlField] = server.URL + "/test.pdf"
		}

		// Start the main program if not already running
		if cmd == nil {
			cmd, _ = runMainProgram(t, tc.boardType)
			time.Sleep(2 * time.Second)
		}

		// Insert document to trigger processing
		golog.Debug("Inserting document with ID:", rawOne["_id"])
		if _, err := watchColl.InsertOne(context.Background(), rawOne); err != nil {
			t.Fatalf("Failed to insert document: %v", err)
		}
		golog.Debug("Document inserted successfully")

		// Wait longer for processing to ensure the server has enough time to start and process requests
		time.Sleep(8 * time.Second)

		// Verify files are downloaded
		verifyDownloadedFiles(t, rawOne, phoUrls, tc, storagePaths, false)

		var mergedOne bson.M
		err := mergedColl.FindOneAndUpdate(
			context.Background(),
			bson.M{"_id": tc.documentID},
			bson.M{"$set": bson.M{}}, // Empty update to trigger retrieval
			options.FindOneAndUpdate().SetReturnDocument(options.After),
		).Decode(&mergedOne)
		if err != nil {
			t.Fatalf("Failed to find merged document: %v", err)
		}

		golog.Debug("Retrieved merged document",
			"documentID", tc.documentID,
			"collection", goresodownload.BoardMergedTable[gBoardType],
			"document", mergedOne)

		// Verify fields
		phoValue, exists := mergedOne[phoField]
		if !exists {
			t.Fatalf("phoLH field not found in merged document")
		}
		phoArray, ok := phoValue.(primitive.A)
		if !ok {
			t.Fatalf("phoLH field is not an array")
		}

		golog.Debug("Found phoLH array",
			"expected", []int32{tc.mediaKeys.Media1_replace, tc.mediaKeys.Media2_replace},
			"actual", phoArray)

		assert.Equal(t, 2, len(phoArray))
		assert.Equal(t, tc.mediaKeys.Media1, phoArray[0].(int32))
		assert.Equal(t, tc.mediaKeys.Media2, phoArray[1].(int32))

		// Verify tnLH field
		tnValue, exists := mergedOne[tnField]
		if !exists {
			t.Fatalf("tnLH field not found in merged document")
		}
		assert.Equal(t, tc.mediaKeys.Media1Thumb, tnValue.(int32))

		if tc.boardType == "TRB" {
			assert.Equal(t, tc.mediaKeys.Doc1, mergedOne[docField].(primitive.A)[0].(string))
			assert.Equal(t, tc.mediaKeys.Doc2, mergedOne[docField].(primitive.A)[1].(string))
		} else {
			assert.Empty(t, mergedOne[docField])
		}
	})

	// Test Replace
	t.Run("Replace", func(t *testing.T) {
		var OldOne bson.M
		if err := watchColl.FindOne(context.Background(), bson.M{"_id": tc.documentID}).Decode(&OldOne); err != nil {
			t.Fatalf("Failed to find document: %v", err)
		}

		// Update media URLs for replace test
		mediaValue, exists := OldOne[tc.mediaField]
		if !exists {
			t.Fatalf("Media field not found in document")
		}
		phoUrls, ok := mediaValue.(primitive.A)
		if !ok {
			t.Fatalf("Media field is not an array")
		}

		phoUrls[0].(primitive.M)[urlField] = server.URL + "/success2.jpg"
		phoUrls[1].(primitive.M)[urlField] = server.URL + "/success1.jpg"
		oldMediaKey0 := phoUrls[0].(primitive.M)[keyField].(string)
		oldMediaKey1 := phoUrls[1].(primitive.M)[keyField].(string)
		phoUrls[0].(primitive.M)[keyField] = oldMediaKey0 + "_replace"
		phoUrls[1].(primitive.M)[keyField] = oldMediaKey1 + "_replace"
		if tc.boardType == "TRB" {
			phoUrls[2].(primitive.M)[urlField] = server.URL + "/audio.mp3"
			phoUrls[3].(primitive.M)[urlField] = server.URL + "/test.pdf"
			oldMediaKey2 := phoUrls[2].(primitive.M)[keyField].(string)
			oldMediaKey3 := phoUrls[3].(primitive.M)[keyField].(string)
			phoUrls[2].(primitive.M)[keyField] = oldMediaKey2 + "_replace"
			phoUrls[3].(primitive.M)[keyField] = oldMediaKey3 + "_replace"
		}

		// Replace document using MongoDB replace command
		opts := options.Replace().SetUpsert(true)
		if _, err := watchColl.ReplaceOne(context.Background(), bson.M{"_id": tc.documentID}, OldOne, opts); err != nil {
			t.Fatalf("Failed to replace document: %v", err)
		}

		// Wait for processing
		time.Sleep(5 * time.Second)

		// Verify files are downloaded
		verifyDownloadedFiles(t, rawOne, phoUrls, tc, storagePaths, false)

		// Verify merged document
		var mergedOne bson.M
		err := mergedColl.FindOneAndUpdate(
			context.Background(),
			bson.M{"_id": tc.documentID},
			bson.M{"$set": bson.M{}}, // Empty update to trigger retrieval
			options.FindOneAndUpdate().SetReturnDocument(options.After),
		).Decode(&mergedOne)
		if err != nil {
			t.Fatalf("Failed to find merged document: %v", err)
		}

		golog.Debug("Retrieved merged document",
			"documentID", tc.documentID,
			"collection", goresodownload.BoardMergedTable[gBoardType],
			"document", mergedOne)

		// Verify fields
		phoValue, exists := mergedOne[phoField]
		if !exists {
			t.Fatalf("phoLH field not found in merged document")
		}
		phoArray, ok := phoValue.(primitive.A)
		if !ok {
			t.Fatalf("phoLH field is not an array")
		}

		golog.Debug("Found phoLH array",
			"expected", []int32{tc.mediaKeys.Media1_replace, tc.mediaKeys.Media2_replace},
			"actual", phoArray)

		assert.Equal(t, 2, len(phoArray))
		assert.Equal(t, tc.mediaKeys.Media1_replace, phoArray[0].(int32))
		assert.Equal(t, tc.mediaKeys.Media2_replace, phoArray[1].(int32))

		// Verify tnLH field
		tnValue, exists := mergedOne[tnField]
		if !exists {
			t.Fatalf("tnLH field not found in merged document")
		}
		assert.Equal(t, tc.mediaKeys.Media1_tn_replace, tnValue.(int32))

		if tc.boardType == "TRB" {
			assert.Equal(t, tc.mediaKeys.Doc1_replace, mergedOne[docField].(primitive.A)[0].(string))
			assert.Equal(t, tc.mediaKeys.Doc2_replace, mergedOne[docField].(primitive.A)[1].(string))
		} else {
			assert.Empty(t, mergedOne[docField])
		}
	})

	// Test Update
	t.Run("Update", func(t *testing.T) {
		var OldOne bson.M
		if err := watchColl.FindOne(context.Background(), bson.M{"_id": tc.documentID}).Decode(&OldOne); err != nil {
			t.Fatalf("Failed to find document: %v", err)
		}

		// Update media URLs
		mediaValue, exists := OldOne[tc.mediaField]
		if !exists {
			t.Fatalf("Media field not found in document")
		}
		phoUrls, ok := mediaValue.(primitive.A)
		if !ok {
			t.Fatalf("Media field is not an array")
		}

		phoUrls = phoUrls[:1]

		// Update document
		update := bson.M{
			"$set": bson.M{
				tc.mediaField: phoUrls,
			},
		}
		if _, err := watchColl.UpdateOne(context.Background(), bson.M{"_id": tc.documentID}, update); err != nil {
			t.Fatalf("Failed to update document: %v", err)
		}

		// Wait for processing with shorter timeout
		time.Sleep(10 * time.Second)

		// Verify files are downloaded
		verifyDownloadedFiles(t, rawOne, phoUrls, tc, storagePaths, true)

		// Verify merged document
		var mergedOne bson.M
		err := mergedColl.FindOneAndUpdate(
			context.Background(),
			bson.M{"_id": tc.documentID},
			bson.M{"$set": bson.M{}}, // empty update to just get the document
			options.FindOneAndUpdate().SetReturnDocument(options.After),
		).Decode(&mergedOne)
		if err != nil {
			t.Fatalf("Failed to find merged document: %v", err)
		}

		// Verify fields
		phoValue, exists := mergedOne[phoField]
		if !exists {
			t.Fatalf("phoLH field not found in merged document")
		}
		phoArray, ok := phoValue.(primitive.A)
		if !ok {
			t.Fatalf("phoLH field is not an array")
		}
		assert.Equal(t, 1, len(phoArray))
		assert.Equal(t, tc.mediaKeys.Media1_replace, phoArray[0].(int32))
		assert.Equal(t, tc.mediaKeys.Media1_tn_replace, mergedOne[tnField].(int32))
	})

}

func TestE2EImageFailure(t *testing.T) {
	testCases := []TestCase{
		{
			name:       "Image Failure Test",
			boardType:  "TRB",
			documentID: "TRBX12213558",
			sidField:   "ListingKey",
			mediaField: "media",
			mediaKeys: MediaKeys{
				Media1:      levelStore.MurmurToInt32("15d621c7-4fd4-4b93-b746-c64bcdd53dde-nw"),
				Media2:      levelStore.MurmurToInt32("f45f2f83-b822-421b-9264-d5c4d542edd3"),
				Media1Thumb: levelStore.MurmurToInt32("15d621c7-4fd4-4b93-b746-c64bcdd53dde-nw-t"),
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			server, cleanup := setupE2ETest(t)
			defer cleanup()

			// Set board type
			gBoardType = tc.boardType
			storagePaths, foundErr := levelStore.GetImageDir(gBoardType)
			if foundErr != nil {
				t.Fatalf("Failed to get image directories for %s: %v", gBoardType, foundErr)
			}
			// delete the storage paths
			for _, path := range storagePaths {
				if err := os.RemoveAll(path); err != nil {
					t.Fatalf("Failed to delete storage path: %v", err)
				}
				if err := os.MkdirAll(path, 0755); err != nil {
					t.Fatalf("Failed to create storage path: %v", err)
				}
			}

			// Start the main program
			cmd, cancel := runMainProgram(t, tc.boardType)
			defer func() {
				cleanupMainProcess(t, cmd, cancel)
			}()

			// get the raw one
			watchColl := gomongo.Coll("rni", goresodownload.BoardWatchTable[gBoardType])
			var rawOne bson.M
			if err := watchColl.FindOne(context.Background(), bson.M{"_id": tc.documentID}).Decode(&rawOne); err != nil {
				t.Fatalf("Failed to find document: %v", err)
			}

			// delete the raw one
			if _, err := watchColl.DeleteOne(context.Background(), bson.M{"_id": tc.documentID}); err != nil {
				t.Fatalf("Failed to delete document: %v", err)
			}

			// Update media URLs to include error cases
			mediaValue, exists := rawOne[tc.mediaField]
			if !exists {
				t.Fatalf("Media field not found in document")
			}
			phoUrls, ok := mediaValue.(primitive.A)
			if !ok {
				t.Fatalf("Media field is not an array")
			}

			// Ensure we have at least 2 elements
			if len(phoUrls) < 2 {
				t.Fatalf("Media array should have at least 2 elements")
			}

			// Set first image to error URL
			phoUrls[0].(primitive.M)["url"] = server.URL + "/error.jpg"
			phoUrls[1].(primitive.M)["url"] = server.URL + "/success2.jpg"

			// Insert document to trigger processing
			if _, err := watchColl.InsertOne(context.Background(), rawOne); err != nil {
				t.Fatalf("Failed to insert document: %v", err)
			}

			// Wait for processing with retry
			failedColl := gomongo.Coll("rni", "reso_photo_download_failed")
			var failedTask bson.M
			var err error
			maxRetries := 30 // Increased to 30 seconds to match HTTP client timeout
			retryInterval := time.Second

			for i := 0; i < maxRetries; i++ {
				time.Sleep(retryInterval)
				err = failedColl.FindOne(context.Background(), bson.M{"prop_id": tc.documentID}).Decode(&failedTask)
				if err == nil {
					break
				}
				if i == maxRetries-1 {
					// Check if process is still running
					if cmd.Process == nil {
						t.Fatalf("Main program process died unexpectedly")
					}
					t.Fatalf("Failed task not recorded after %d seconds: %v", maxRetries, err)
				}
				golog.Debug("Waiting for failed task to be recorded",
					"attempt", i+1,
					"error", err,
					"timeout", maxRetries)
			}

			// Verify failed task was recorded
			assert.NoError(t, err, "Failed task should be recorded")
			assert.NotEmpty(t, failedTask["image_url"], "Failed task should have an image URL")
			assert.GreaterOrEqual(t, failedTask["retry_count"].(int32), int32(1), "Retry count should be at least 1")
			errMsg := failedTask["err_msg"].(string)
			assert.True(t,
				strings.Contains(errMsg, "failed to download file") ||
					strings.Contains(errMsg, "context deadline exceeded") ||
					strings.Contains(errMsg, "timeout"),
				"Error message should indicate download failure or timeout: %s", errMsg)

			// Clean up the failed task
			if _, err := failedColl.DeleteOne(context.Background(), bson.M{"prop_id": tc.documentID}); err != nil {
				t.Logf("Failed to delete failed task: %v", err)
			}
		})
	}
}

func TestE2EImageTimeout(t *testing.T) {
	testCases := []TestCase{
		{
			name:       "Image Timeout Test",
			boardType:  "TRB",
			documentID: "TRBX12213558",
			sidField:   "ListingKey",
			mediaField: "media",
			mediaKeys: MediaKeys{
				Media1:      levelStore.MurmurToInt32("15d621c7-4fd4-4b93-b746-c64bcdd53dde-nw"),
				Media2:      levelStore.MurmurToInt32("f45f2f83-b822-421b-9264-d5c4d542edd3"),
				Media1Thumb: levelStore.MurmurToInt32("15d621c7-4fd4-4b93-b746-c64bcdd53dde-nw-t"),
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			server, cleanup := setupE2ETest(t)
			defer cleanup()

			// Set board type
			gBoardType = tc.boardType
			storagePaths, foundErr := levelStore.GetImageDir(gBoardType)
			if foundErr != nil {
				t.Fatalf("Failed to get image directories for %s: %v", gBoardType, foundErr)
			}
			// delete the storage paths
			for _, path := range storagePaths {
				if err := os.RemoveAll(path); err != nil {
					t.Fatalf("Failed to delete storage path: %v", err)
				}
				if err := os.MkdirAll(path, 0755); err != nil {
					t.Fatalf("Failed to create storage path: %v", err)
				}
			}

			// Start the main program
			cmd, cancel := runMainProgram(t, tc.boardType)
			defer func() {
				cleanupMainProcess(t, cmd, cancel)
			}()

			// get the raw one
			watchColl := gomongo.Coll("rni", goresodownload.BoardWatchTable[gBoardType])
			var rawOne bson.M
			if err := watchColl.FindOne(context.Background(), bson.M{"_id": tc.documentID}).Decode(&rawOne); err != nil {
				t.Fatalf("Failed to find document: %v", err)
			}

			// delete the raw one
			if _, err := watchColl.DeleteOne(context.Background(), bson.M{"_id": tc.documentID}); err != nil {
				t.Fatalf("Failed to delete document: %v", err)
			}

			// Update media URLs to include timeout case
			mediaValue, exists := rawOne[tc.mediaField]
			if !exists {
				t.Fatalf("Media field not found in document")
			}
			phoUrls, ok := mediaValue.(primitive.A)
			if !ok {
				t.Fatalf("Media field is not an array")
			}

			// Ensure we have at least 2 elements
			if len(phoUrls) < 2 {
				t.Fatalf("Media array should have at least 2 elements")
			}

			// Set first image to timeout URL
			phoUrls[0].(primitive.M)["url"] = server.URL + "/timeout.jpg"
			phoUrls[1].(primitive.M)["url"] = server.URL + "/success2.jpg"

			// Insert document to trigger processing
			if _, err := watchColl.InsertOne(context.Background(), rawOne); err != nil {
				t.Fatalf("Failed to insert document: %v", err)
			}

			// Wait for processing with retry
			failedColl := gomongo.Coll("rni", "reso_photo_download_failed")
			var failedTask bson.M
			var err error
			maxRetries := 30 // Increased to 30 seconds to match HTTP client timeout
			retryInterval := time.Second

			for i := 0; i < maxRetries; i++ {
				time.Sleep(retryInterval)
				err = failedColl.FindOne(context.Background(), bson.M{"prop_id": tc.documentID}).Decode(&failedTask)
				if err == nil {
					break
				}
				if i == maxRetries-1 {
					// Check if process is still running
					if cmd.Process == nil {
						t.Fatalf("Main program process died unexpectedly")
					}
					t.Fatalf("Failed task not recorded after %d seconds: %v", maxRetries, err)
				}
				golog.Debug("Waiting for failed task to be recorded",
					"attempt", i+1,
					"error", err,
					"timeout", maxRetries)
			}

			// Verify failed task was recorded
			assert.NoError(t, err, "Failed task should be recorded")
			assert.NotEmpty(t, failedTask["image_url"], "Failed task should have an image URL")
			assert.GreaterOrEqual(t, failedTask["retry_count"].(int32), int32(1), "Retry count should be at least 1")
			errMsg := failedTask["err_msg"].(string)
			assert.True(t,
				strings.Contains(errMsg, "failed to download file") ||
					strings.Contains(errMsg, "context deadline exceeded") ||
					strings.Contains(errMsg, "timeout"),
				"Error message should indicate download failure or timeout: %s", errMsg)

			// Clean up the failed task
			if _, err := failedColl.DeleteOne(context.Background(), bson.M{"prop_id": tc.documentID}); err != nil {
				t.Logf("Failed to delete failed task: %v", err)
			}
		})
	}
}
