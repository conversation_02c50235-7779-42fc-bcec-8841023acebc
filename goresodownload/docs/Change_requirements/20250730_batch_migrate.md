# 需求 [batch_migrate]

## 反馈

1. Fred反馈需要对目录'/1200'下的图片进行迁移

## 需求提出人:   Fred

## 修改人：      <PERSON><PERSON><PERSON><PERSON>ei

## 提出日期:     2025-07-26

## 原因

1. 旧的L1计算导致目录'/1200'下文件过多,需要添加batch使用新的L1计算逻辑对文件进行添加

## Batch的逻辑

1. 根据指定的`board`查找rni下的房源数据。
2. 对记录中的`phoP`与新计算的`phoP`不一致的房源图片进行迁移。

### 迁移逻辑

1. 提取房源的图片hash列表
2. 旧的图片有缺失时,删除已下载的图片,unset数据库相关字段,加入queue中重新下载,修改统计信息
3. 使用硬链接迁移文件
4. 迁移成功后更新数据库与统计信息,删除源图片
5. 迁移失败时回滚已迁移的文件并记录错误

## 是否需要补充UT

1. 不需要补充UT

## 确认日期:    2025-07-30

## online-step

1. 运行batch，对图片进行迁移,采用方案B: `ca6，ca7同时硬链接到新目录-->merged 修改phoP-->删除源图片`
```
./start.sh  -n migrate_trbPic -d "goresodownload" -cmd "cmd/batch/migrate_trbPic/main.go -board=TRB -dir=/1200 -dryrun"
```