package golog

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"os"
	"path/filepath"
	"runtime"
	"strings"

	goconfig "github.com/real-rm/goconfig"
)

// Logger is the global logger instance
var (
	Logger *slog.Logger = slog.New(&customHandler{
		w:        os.Stdout,
		minLevel: slog.LevelDebug,
	})
)

// customHandler implements slog.Handler interface for custom log formatting
type customHandler struct {
	w          io.Writer
	minLevel   slog.Level
	levelFiles map[slog.Level]io.Writer
}

// Enabled returns true if the level is greater than or equal to the minimum level
func (h *customHandler) Enabled(ctx context.Context, level slog.Level) bool {
	return level >= h.minLevel
}

// WithAttrs returns the same handler as we don't need to handle attributes differently
func (h *customHandler) WithAttrs(attrs []slog.Attr) slog.Handler {
	return h
}

// WithGroup returns the same handler as we don't need to handle groups differently
func (h *customHandler) WithGroup(name string) slog.Handler {
	return h
}

// parseAndFormatJSON attempts to parse a string as JSON and format it as pretty JSON
func parseAndFormatJSON(str string) (string, error) {
	var parsedJSON interface{}
	err := json.Unmarshal([]byte(str), &parsedJSON)
	if err == nil {
		prettyJSON, err := json.MarshalIndent(parsedJSON, "", "  ")
		if err == nil {
			return string(prettyJSON), nil
		}
	}
	return "", err
}

// tryFormatJSON attempts to format a value as pretty JSON
// If the value is already a JSON string, it will be parsed and reformatted
func tryFormatJSON(value interface{}) string {
	// step 1: try to parse as JSON and format
	if str, ok := value.(string); ok {
		if prettyJSON, err := parseAndFormatJSON(str); err == nil {
			return prettyJSON
		}
	}

	// step 2: try to format as JSON
	jsonBytes, err := json.MarshalIndent(value, "", "  ")
	if err == nil {
		return string(jsonBytes)
	}

	// step 3: return string representation
	return fmt.Sprintf("%v", value)
}

// Handle processes a log record and writes it to the output
func (h *customHandler) Handle(ctx context.Context, r slog.Record) error {
	var buf strings.Builder

	// Write timestamp
	buf.WriteString(r.Time.Format("2006-01-02T15:04:05.999-07:00"))
	buf.WriteString(" level=")
	buf.WriteString(r.Level.String())

	// Check if there are any attributes
	hasAttrs := false
	r.Attrs(func(a slog.Attr) bool {
		hasAttrs = true
		return false
	})

	if hasAttrs {
		// If there are attributes, process them as key-value pairs
		if r.Message != "" {
			buf.WriteString(" msg=")
			buf.WriteString(r.Message)
		}
		attrs := make(map[string]interface{})
		r.Attrs(func(a slog.Attr) bool {
			if a.Key != slog.TimeKey && a.Key != slog.LevelKey && a.Key != slog.MessageKey {
				attrs[a.Key] = tryFormatJSON(a.Value.Any())
			}
			return true
		})

		// Write formatted attributes
		for k, v := range attrs {
			buf.WriteString(", ")
			buf.WriteString(k)
			buf.WriteString("=")
			buf.WriteString(fmt.Sprintf("%v", v))
		}
	} else {
		// If no attributes, try to format the message as JSON
		buf.WriteString(" ")
		buf.WriteString(tryFormatJSON(r.Message))
	}

	buf.WriteString("\n")
	logLine := buf.String()

	// Write to stdout if configured
	if h.w != nil {
		if _, err := h.w.Write([]byte(logLine)); err != nil {
			return err
		}
	}

	// Write to level-specific file if available
	if levelWriter, ok := h.levelFiles[r.Level]; ok {
		if _, err := levelWriter.Write([]byte(logLine)); err != nil {
			return err
		}
	}

	return nil
}

// createLogger creates a new logger instance with the specified log file
func createLogger(logFile string) (*slog.Logger, error) {
	// Create log directory if it doesn't exist
	dir := filepath.Dir(logFile)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return nil, err
	}

	// Open log file
	file, err := os.OpenFile(logFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return nil, err
	}

	// Create multi-writer for both file and stdout
	multiWriter := io.MultiWriter(file, os.Stdout)
	handler := &customHandler{w: multiWriter}

	// Create and return logger
	logger := slog.New(handler)
	return logger, nil
}

// Init initializes the logger with the specified log file
func Init(logFile string) error {
	logger, err := createLogger(logFile)
	if err != nil {
		return err
	}
	Logger = logger
	return nil
}

// InitLog initializes the logger with default log file path
func InitLog() error {
	// Get log directory from config
	var dir string
	if dirConfig := goconfig.Config("golog.dir"); dirConfig != nil {
		if d, ok := dirConfig.(string); ok {
			dir = d
		} else {
			dir = "logs" // Default if type assertion fails
		}
	} else {
		dir = "logs" // Default if config is nil
	}

	// Check if standard output is enabled
	standardOutput := false
	if stdOutConfig := goconfig.Config("golog.standard_output"); stdOutConfig != nil {
		if so, ok := stdOutConfig.(bool); ok {
			standardOutput = so
		}
	}

	// Create log directory if it doesn't exist
	if err := os.MkdirAll(dir, 0755); err != nil {
		return err
	}

	// Get log level from config
	minLevel := slog.LevelDebug // Default level
	if levelConfig := goconfig.Config("golog.level"); levelConfig != nil {
		if level, ok := levelConfig.(string); ok {
			switch strings.ToLower(level) {
			case "debug":
				minLevel = slog.LevelDebug
			case "info":
				minLevel = slog.LevelInfo
			case "warn":
				minLevel = slog.LevelWarn
			case "error":
				minLevel = slog.LevelError
			}
		}
	}

	// Create level-specific log files
	levelFiles := make(map[slog.Level]io.Writer)

	// Verbose/Debug log file
	if verboseFile := goconfig.Config("golog.verbose"); verboseFile != nil {
		if vf, ok := verboseFile.(string); ok && vf != "" {
			file, err := os.OpenFile(filepath.Join(dir, vf), os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
			if err != nil {
				return err
			}
			levelFiles[slog.LevelDebug] = file
		}
	}

	// Info log file
	if infoFile := goconfig.Config("golog.info"); infoFile != nil {
		if inf, ok := infoFile.(string); ok && inf != "" {
			file, err := os.OpenFile(filepath.Join(dir, inf), os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
			if err != nil {
				return err
			}
			levelFiles[slog.LevelInfo] = file
		}
	}

	// Error log file
	if errorFile := goconfig.Config("golog.error"); errorFile != nil {
		if ef, ok := errorFile.(string); ok && ef != "" {
			file, err := os.OpenFile(filepath.Join(dir, ef), os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
			if err != nil {
				return err
			}
			levelFiles[slog.LevelError] = file
		}
	}

	// Create handler with appropriate writer
	var writer io.Writer
	if standardOutput {
		writer = os.Stdout
	}

	handler := &customHandler{
		w:          writer,
		minLevel:   minLevel,
		levelFiles: levelFiles,
	}
	Logger = slog.New(handler)

	return nil
}

// getFileInfo returns the caller's file name and line number
func getFileInfo() string {
	_, file, line, _ := runtime.Caller(2) // Use 2 to skip the getFileInfo function itself
	return fmt.Sprintf("%s:%d", filepath.Base(file), line)
}

// addFileInfo adds file information to the log arguments
func addFileInfo(args []interface{}, fileInfo string) []interface{} {
	if len(args)%2 != 0 {
		args = append(args, "value") // placeholder key
	}
	return append(args, "file", fileInfo)
}

// Debug logs a message at debug level
func Debug(msg interface{}, args ...interface{}) {
	if len(args) == 0 {
		// If no args, try to format msg as JSON
		Logger.Debug("", "value", msg)
	} else {
		// If first arg is string, use it as message
		if msgStr, ok := msg.(string); ok {
			Logger.Debug(msgStr, args...)
		} else {
			// If first arg is not string, treat it as value
			Logger.Debug("", append([]interface{}{"value", msg}, args...)...)
		}
	}
}

// Debugf logs a formatted message at debug level
func Debugf(format string, args ...interface{}) {
	Logger.Debug(fmt.Sprintf(format, args...))
}

// Verbose logs a message at info level (alias for Info)
func Verbose(msg interface{}, args ...interface{}) {
	if len(args) == 0 {
		// If no args, try to format msg as JSON
		Logger.Debug("", "value", msg)
	} else {
		// If first arg is string, use it as message
		if msgStr, ok := msg.(string); ok {
			Logger.Debug(msgStr, args...)
		} else {
			// If first arg is not string, treat it as value
			Logger.Debug("", append([]interface{}{"value", msg}, args...)...)
		}
	}
}

// Verbosef logs a formatted message at info level (alias for Infof)
func Verbosef(format string, args ...interface{}) {
	Logger.Debug(fmt.Sprintf(format, args...))
}

// Info logs a message at info level
func Info(msg interface{}, args ...interface{}) {
	if len(args) == 0 {
		// If no args, try to format msg as JSON
		Logger.Info("", "value", msg)
	} else {
		// If first arg is string, use it as message
		if msgStr, ok := msg.(string); ok {
			Logger.Info(msgStr, args...)
		} else {
			// If first arg is not string, treat it as value
			Logger.Info("", append([]interface{}{"value", msg}, args...)...)
		}
	}
}

// Infof logs a formatted message at info level
func Infof(format string, args ...interface{}) {
	Logger.Info(fmt.Sprintf(format, args...))
}

// Warn logs a message at warn level
func Warn(msg interface{}, args ...interface{}) {
	fileInfo := getFileInfo()

	if len(args) == 0 {
		// If no args, try to format msg as JSON
		Logger.Warn("", "value", msg, "file", fileInfo)
	} else {
		// If first arg is string, use it as message
		if msgStr, ok := msg.(string); ok {
			Logger.Warn(msgStr, addFileInfo(args, fileInfo)...)
		} else {
			// If first arg is not string, treat it as value
			Logger.Warn("", addFileInfo(append([]interface{}{"value", msg}, args...), fileInfo)...)
		}
	}
}

// Warnf logs a formatted message at warn level
func Warnf(format string, args ...interface{}) {
	fileInfo := getFileInfo()
	Logger.Warn(fmt.Sprintf(format, args...), "file", fileInfo)
}

// Error logs a message at error level
func Error(msg interface{}, args ...interface{}) {
	fileInfo := getFileInfo()

	if len(args) == 0 {
		// If no args, try to format msg as JSON
		Logger.Error("", "value", msg, "file", fileInfo)
	} else {
		// If first arg is string, use it as message
		if msgStr, ok := msg.(string); ok {
			Logger.Error(msgStr, addFileInfo(args, fileInfo)...)
		} else {
			// If first arg is not string, treat it as value
			Logger.Error("", addFileInfo(append([]interface{}{"value", msg}, args...), fileInfo)...)
		}
	}
}

// Errorf logs a formatted message at error level
func Errorf(format string, args ...interface{}) {
	fileInfo := getFileInfo()
	Logger.Error(fmt.Sprintf(format, args...), "file", fileInfo)
}

// Fatal logs a message at error level
func Fatal(msg string, args ...interface{}) {
	fileInfo := getFileInfo()
	Logger.Error(msg, addFileInfo(args, fileInfo)...)
	panic(msg)
}

// Fatalf logs a formatted message at error level
func Fatalf(format string, args ...interface{}) {
	fileInfo := getFileInfo()
	msg := fmt.Sprintf(format, args...)
	Logger.Error(msg, "file", fileInfo)
	panic(msg)
}

// SetStdoutWriter sets the stdout writer for the logger
func SetStdoutWriter(w io.Writer) {
	if handler, ok := Logger.Handler().(*customHandler); ok {
		handler.w = w
	}
}
