package golog

import (
	"bytes"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"

	goconfig "github.com/real-rm/goconfig"
)

// resetConfig resets the goconfig state
func resetConfig() {
	goconfig.ResetConfig()
}

// captureStdout captures stdout output and returns it as a string
func captureStdout(f func()) string {
	old := os.Stdout
	r, w, _ := os.Pipe()
	os.Stdout = w
	outC := make(chan string)
	go func() {
		var buf bytes.Buffer
		if _, err := io.Copy(&buf, r); err != nil {
			fmt.Printf("Failed to copy stdout: %v", err)
		}
		outC <- buf.String()
	}()
	f()
	if err := w.Close(); err != nil {
		fmt.Printf("Failed to close stdout: %v", err)
	}
	os.Stdout = old
	return <-outC
}

func TestCaptureStdout(t *testing.T) {
	stdout := captureStdout(func() {
		fmt.Println("This is a test")
	})
	if !strings.Contains(stdout, "This is a test") {
		t.Error("captureStdout did not capture the output")
	}
}

func TestInitLog(t *testing.T) {
	// Create temporary directory for logs
	tmpDir, err := os.MkdirTemp("", "logs-*")
	fmt.Println("tmpDir", tmpDir)
	if err != nil {
		t.Fatal(err)
	}
	defer func() {
		if err := os.RemoveAll(tmpDir); err != nil {
			fmt.Printf("Failed to remove temporary directory: %v", err)
		}
	}()

	// Test cases for different configurations
	testCases := []struct {
		name           string
		config         string
		standardOutput bool
		expectStdout   bool
	}{
		{
			name: "Without standard output",
			config: `
[golog]
dir = "` + tmpDir + `"
level = "debug"
verbose = "verbose.log"
info = "info.log"
error = "error.log"
format = "text"
`,
			standardOutput: false,
			expectStdout:   false,
		},
		{
			name: "With standard output",
			config: `
[golog]
dir = "` + tmpDir + `"
level = "debug"
verbose = "verbose.log"
info = "info.log"
error = "error.log"
format = "text"
standard_output = true
`,
			standardOutput: true,
			expectStdout:   true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Reset config state before each test case
			resetConfig()

			// Create a temporary config file
			tmpfile, err := os.CreateTemp(tmpDir, "config-*.toml")
			fmt.Println("tmpfile", tmpfile.Name())
			if err != nil {
				t.Fatal(err)
			}
			configPath := tmpfile.Name()
			defer func() {
				if err := os.Remove(configPath); err != nil {
					fmt.Printf("Failed to remove config file: %v", err)
				}
			}()

			// Write config content
			if _, err := tmpfile.Write([]byte(tc.config)); err != nil {
				t.Fatal(err)
			}
			if err := tmpfile.Sync(); err != nil {
				t.Fatal(err)
			}
			if err := tmpfile.Close(); err != nil {
				t.Fatal(err)
			}
			// Set config file path and load configuration
			if err := os.Setenv("RMBASE_FILE_CFG", configPath); err != nil {
				t.Fatal(err)
			}

			// Wait a bit to ensure file system sync
			time.Sleep(100 * time.Millisecond)

			// Load config and verify it exists
			if _, err := os.Stat(configPath); err != nil {
				t.Fatalf("Config file does not exist: %v", err)
			}

			// Read and verify config content
			content, err := os.ReadFile(configPath)
			if err != nil {
				t.Fatalf("Failed to read config file: %v", err)
			}
			if !strings.Contains(string(content), tc.config) {
				t.Fatal("Config file content mismatch")
			}

			if err := goconfig.LoadConfig(); err != nil {
				t.Fatalf("Failed to load config: %v", err)
			}

			// Initialize logger
			if err := InitLog(); err != nil {
				t.Fatalf("InitLog() error = %v", err)
			}

			// Override the logger's output to use the test directory
			if handler, ok := Logger.Handler().(*customHandler); ok {
				// Create a new file in the test directory
				logFile := filepath.Join(tmpDir, "app.log")
				file, err := os.OpenFile(logFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
				if err != nil {
					t.Fatalf("Failed to create log file: %v", err)
				}
				defer func() {
					if err := file.Close(); err != nil {
						t.Fatalf("Failed to close log file: %v", err)
					}
				}()

				// Use both file and stdout if standard_output is true
				if tc.standardOutput {
					// Create a pipe to capture output
					r, w, _ := os.Pipe()
					oldStdout := os.Stdout
					os.Stdout = w
					outC := make(chan string)
					go func() {
						var buf bytes.Buffer
						if _, err := io.Copy(&buf, r); err != nil {
							fmt.Printf("Failed to copy stdout: %v", err)
						}
						outC <- buf.String()
					}()

					handler.w = io.MultiWriter(file, w)

					// Test logging to different levels
					tests := []struct {
						name     string
						logFunc  func(interface{}, ...interface{})
						logFile  string
						message  string
						contains []string
					}{
						{
							name:     "Debug",
							logFunc:  Debug,
							logFile:  "verbose.log",
							message:  "debug message",
							contains: []string{"debug message"},
						},
						{
							name:     "Info",
							logFunc:  Info,
							logFile:  "info.log",
							message:  "info message",
							contains: []string{"info message"},
						},
						{
							name:     "Error",
							logFunc:  Error,
							logFile:  "error.log",
							message:  "error message",
							contains: []string{"error message"},
						},
						{
							name:     "Verbose",
							logFunc:  Verbose,
							logFile:  "verbose.log",
							message:  "verbose message",
							contains: []string{"verbose message"},
						},
					}

					for _, tt := range tests {
						t.Run(tt.name, func(t *testing.T) {
							// Execute the logging function
							tt.logFunc(tt.message)

							// Read log file
							content, err := os.ReadFile(filepath.Join(tmpDir, tt.logFile))
							if err != nil {
								t.Fatalf("Failed to read log file: %v", err)
							}

							// Print log content for debugging
							fmt.Printf("Log content: %s\n", string(content))

							// Check if log contains expected content in file
							logStr := string(content)
							for _, substr := range tt.contains {
								if !strings.Contains(logStr, substr) {
									t.Errorf("Log file doesn't contain %q", substr)
								}
							}
						})
					}

					// Close the pipe and restore stdout
					if err := w.Close(); err != nil {
						t.Fatalf("Failed to close stdout: %v", err)
					}
					os.Stdout = oldStdout
					stdout := <-outC

					// Check stdout output
					for _, tt := range tests {
						for _, substr := range tt.contains {
							if !strings.Contains(stdout, substr) {
								t.Errorf("Stdout doesn't contain %q, got: %q", substr, stdout)
							}
						}
					}
				} else {
					handler.w = file

					// Test logging to different levels
					tests := []struct {
						name     string
						logFunc  func(interface{}, ...interface{})
						logFile  string
						message  string
						contains []string
					}{
						{
							name:     "Debug",
							logFunc:  Debug,
							logFile:  "verbose.log",
							message:  "debug message",
							contains: []string{"debug message"},
						},
						{
							name:     "Info",
							logFunc:  Info,
							logFile:  "info.log",
							message:  "info message",
							contains: []string{"info message"},
						},
						{
							name:     "Error",
							logFunc:  Error,
							logFile:  "error.log",
							message:  "error message",
							contains: []string{"error message"},
						},
						{
							name:     "Verbose",
							logFunc:  Verbose,
							logFile:  "verbose.log",
							message:  "verbose message",
							contains: []string{"verbose message"},
						},
					}

					for _, tt := range tests {
						t.Run(tt.name, func(t *testing.T) {
							// Execute the logging function
							tt.logFunc(tt.message)

							// Read log file
							content, err := os.ReadFile(filepath.Join(tmpDir, tt.logFile))
							if err != nil {
								t.Fatalf("Failed to read log file: %v", err)
							}

							// Print log content for debugging
							fmt.Printf("Log content: %s\n", string(content))

							// Check if log contains expected content in file
							logStr := string(content)
							for _, substr := range tt.contains {
								if !strings.Contains(logStr, substr) {
									t.Errorf("Log file doesn't contain %q", substr)
								}
							}
						})
					}
				}
			}
		})
	}
}
