# gofile

A Go language file handling package that provides advanced image download, processing, and conversion capabilities with robust retry mechanisms and multi-format support.

## Features

### Image Download & Processing
- **Advanced download system** with configurable retry mechanism and exponential backoff
- **Multi-format support**: JPEG and WebP with automatic conversion
- **Image resizing** capabilities with aspect ratio preservation
- **WebP compression** with JPEG fallback for compatibility
- **Multiple path saving** support for redundancy
- **Memory-optimized processing** with configurable limits
- **Streaming downloads** for large files

### File Management
- **Multiple save paths** with optimized copying for non-photo files
- **Temporary file handling** for multi-path operations
- **Automatic format detection** and conversion
- **Comprehensive error handling** with detailed logging
- **Thread-safe operations** with proper resource cleanup

### Performance & Reliability
- **Configurable retry attempts** (default: 3) with exponential backoff
- **Memory management** with configurable limits and garbage collection
- **Timeout handling** (default: 120 seconds)
- **Concurrent processing** support
- **Optimized for both single and multiple file operations**

## Installation

```bash
go get github.com/real-rm/gofile
```

## Quick Start

```go
package main

import (
    "log"

    "github.com/real-rm/gofile"
)

func main() {
    // Download and save a file; set IsPhoto to true for photos
    opts := &gofile.DownloadAndSaveFileOptions{
        URL:          "https://example.com/image.jpg",
        SavePaths:    []string{"/path/to/save/image.webp"},
        IsPhoto:      true,
        CompressWebP: true,
        MaxRetries:   3,
    }

    results, err := gofile.DownloadAndSaveFile(opts)
    if err != nil {
        log.Fatal(err)
    }
    log.Printf("Image saved to: %v", results)
}
```

## Configuration

The package uses a configuration system that supports TOML format. Configure your logging settings in your config file:

```toml
[golog]
dir = "/path/to/logs"
level = "info"
verbose = "verbose.log"
info = "info.log"
error = "error.log"
format = "text"
```

### Memory Configuration (Optional)

The package uses internal memory limits for image processing. These are currently hardcoded constants that require recompilation to modify:

```go
// Internal constants (hardcoded, require recompilation to change)
const (
    MAX_IMAGE_MEMORY_MB  = 100              // Maximum memory for image processing
    MAX_IMAGE_SIZE_BYTES = 10 * 1024 * 1024 // 10MB limit per image
)
```

**Important**: These limits are compiled into the package and cannot be changed at runtime. To modify them:

1. **Fork and modify**: Clone the repository, change the constants in `fetch_and_save.go`, and rebuild
2. **Environment-based approach**: Implement your own wrapper that checks environment variables before calling gofile functions
3. **Application-level limits**: Implement size checks in your application before calling gofile functions

**Example of application-level configuration**:
```go
// In your application
const (
    APP_MAX_IMAGE_SIZE = 20 * 1024 * 1024 // 20MB custom limit
)

func downloadWithCustomLimits(url string, paths []string) error {
    // Pre-check file size via HEAD request
    resp, err := http.Head(url)
    if err != nil {
        return err
    }
    defer resp.Body.Close()

    if contentLength := resp.Header.Get("Content-Length"); contentLength != "" {
        if size, _ := strconv.ParseInt(contentLength, 10, 64); size > APP_MAX_IMAGE_SIZE {
            return fmt.Errorf("file too large: %d bytes > %d bytes", size, APP_MAX_IMAGE_SIZE)
        }
    }

    // Proceed with gofile download
    return gofile.DownloadAndSaveFile(&gofile.DownloadAndSaveFileOptions{
        URL:       url,
        SavePaths: paths,
        IsPhoto:   true,
    })
}
```

## API Reference

### Core Functions

#### DownloadAndSaveFile (Recommended)

The main function for downloading and saving files with advanced options:

```go
type DownloadAndSaveFileOptions struct {
    URL          string   // URL to download from
    SavePaths    []string // Paths to save the file to
    CompressWebP bool     // Whether to compress as WebP (only for images)
    IsPhoto      bool     // Whether the file is a photo
    MaxRetries   int      // Maximum number of retry attempts
}

opts := &gofile.DownloadAndSaveFileOptions{
    URL:          "https://example.com/image.jpg",
    SavePaths:    []string{"/path1/image.webp", "/path2/image.jpg"},
    IsPhoto:      true,        // Enable image processing
    CompressWebP: true,        // Use WebP compression with JPEG fallback
    MaxRetries:   3,           // Number of retry attempts
}

results, err := gofile.DownloadAndSaveFile(opts)
if err != nil {
    log.Printf("Download failed: %v", err)
    return
}
log.Printf("Files saved to: %v", results)
```

#### DownloadAndSaveImageInDirs

Save images to multiple directories with format conversion:

```go
// Returns map[string]string where key is requested path, value is actual saved path
results, err := gofile.DownloadAndSaveImageInDirs(
    "https://example.com/image.jpg",
    []string{"/path1/pic.webp", "/path2/pic.jpg"},
    true, // compressWebP - attempts WebP with JPEG fallback
    3,    // maxRetries (optional)
)
if err != nil {
    log.Printf("Download failed: %v", err)
    return
}
for requestedPath, savedPath := range results {
    log.Printf("Requested: %s, Saved: %s", requestedPath, savedPath)
}
```

#### DownloadAndResizeImage

Download and resize images while maintaining aspect ratio:

```go
img, err := gofile.DownloadAndResizeImage(
    "https://example.com/large-image.jpg",
    800,  // maxWidth
    600,  // maxHeight
)
if err != nil {
    log.Printf("Download and resize failed: %v", err)
    return
}
// img is now a resized image.Image that can be saved
```

#### SaveImage

Save images with format conversion and compression:

```go
// Save as WebP with compression (falls back to JPEG if WebP fails)
savedPath, err := gofile.SaveImage(img, "/path/to/image.webp", true)
if err != nil {
    log.Printf("Save failed: %v", err)
    return
}
log.Printf("Image saved to: %s", savedPath)

// Save as JPEG
savedPath, err := gofile.SaveImage(img, "/path/to/image.jpg", false)
```

## Advanced Features

### Retry Mechanism

The package includes a sophisticated retry mechanism with exponential backoff:

```go
// Built-in retry is automatically used in all download functions
opts := &gofile.DownloadAndSaveFileOptions{
    URL:        "https://example.com/image.jpg",
    SavePaths:  []string{"/path/to/image.jpg"},
    MaxRetries: 5, // Custom retry count (default: 3)
}

results, err := gofile.DownloadAndSaveFile(opts)
```

**Retry Features:**
- **Default attempts**: 3 retries
- **Exponential backoff**: 1s, 2s, 3s delays between attempts
- **Timeout handling**: 120s default timeout per request
- **Error categorization**: Distinguishes between network and HTTP errors
- **Configurable retry count**: Can be customized per request

### Memory Management

The package includes memory optimization features:

**Memory Features:**
- **Automatic garbage collection** after large image processing
- **Memory pressure detection** with configurable limits
- **Streaming downloads** for large files
- **Optimized multi-path saving** (temp file + copy for multiple destinations)

### Format Support & Conversion

```go
// WebP with JPEG fallback
savedPath, err := gofile.SaveImage(img, "/path/image.webp", true)
// If WebP encoding fails, automatically saves as JPEG with .jpg extension

// Direct JPEG saving
savedPath, err := gofile.SaveImage(img, "/path/image.jpg", false)

// Automatic format detection from file extension
// .webp files will attempt WebP compression
// .jpg/.jpeg files will save as JPEG
```

**Format Features:**
- **WebP compression** with automatic JPEG fallback
- **Format detection** from file extensions
- **Quality optimization** for both WebP and JPEG
- **Automatic extension correction** when format conversion occurs

## Error Handling

The package provides comprehensive error handling with detailed logging:

```go
import (
    "log"
    "strings"

    "github.com/real-rm/gofile"
)

func handleDownload() {
    results, err := gofile.DownloadAndSaveFile(opts)
    if err != nil {
        // Check for specific error types
        if strings.Contains(err.Error(), "timeout") {
            log.Printf("Download timed out: %v", err)
            // Handle timeout specifically
        } else if strings.Contains(err.Error(), "failed to save") {
            log.Printf("Save operation failed: %v", err)
            // Handle save errors
        } else {
            log.Printf("General download error: %v", err)
        }
        return
    }

    // Check partial success for multiple paths
    for requestedPath, savedPath := range results {
        if savedPath == "" {
            log.Printf("Failed to save to: %s", requestedPath)
        } else {
            log.Printf("Successfully saved to: %s", savedPath)
        }
    }
}
```

### Performance Optimization

The package includes several performance optimizations:

**Single Path Operations:**
```go
// Most efficient for single file
opts := &gofile.DownloadAndSaveFileOptions{
    URL:       "https://example.com/file.jpg",
    SavePaths: []string{"/single/path.jpg"}, // Direct streaming
    IsPhoto:   true,
}
```

**Multiple Path Operations:**
```go
// Optimized for multiple destinations
opts := &gofile.DownloadAndSaveFileOptions{
    URL:       "https://example.com/file.jpg",
    SavePaths: []string{"/path1.jpg", "/path2.jpg", "/path3.jpg"},
    IsPhoto:   false, // Uses temp file + copy strategy
}
```

**Performance Features:**
- **Direct streaming** for single path operations
- **Temp file + copy** strategy for multiple paths (non-photos)
- **Memory-optimized** image processing with garbage collection
- **Concurrent-safe** operations with proper resource cleanup

## Testing

The package includes comprehensive test coverage with various test scenarios:

```bash
# Run all tests
go test -v

# Run tests with coverage
go test -cover

# Run specific test
go test -run TestDownloadAndSaveFile -v

# Run tests with race detection
go test -race
```

### Test Configuration

Tests require a `local.test.ini` configuration file:

```toml
[golog]
dir = "/tmp/test_logs"
level = "info"
verbose = "verbose.log"
info = "info.log"
error = "error.log"
format = "text"
```

## Dependencies

### Core Dependencies
- **github.com/HugoSmits86/nativewebp** - WebP image encoding and compression
- **github.com/real-rm/golog** - Structured logging with multiple levels

### Standard Library
- **image/jpeg** - JPEG image processing and encoding
- **image/png** - PNG image processing
- **net/http** - HTTP client for downloads
- **os** - File system operations
- **time** - Timeout and retry timing

## Related Packages

This package works well with other packages in the ecosystem:

- **[golevelstore](https://github.com/real-rm/golevelstore)** - Hierarchical directory management with MongoDB integration
- **github.com/real-rm/goconfig** - Configuration management
- **github.com/real-rm/gohelper** - Utility functions and helpers

## Complete Example

```go
package main

import (
    "log"
    "os"

    "github.com/real-rm/gofile"
    "github.com/real-rm/golog"
    "github.com/real-rm/goconfig"
)

func main() {
    // Initialize configuration and logging
    if err := goconfig.LoadConfig(); err != nil {
        log.Fatal("Failed to load config:", err)
    }

    if err := golog.InitLog(); err != nil {
        log.Fatal("Failed to initialize logging:", err)
    }

    // Ensure directories exist
    os.MkdirAll("/tmp/images", 0755)
    os.MkdirAll("/tmp/backup", 0755)

    // Download and save with multiple options
    opts := &gofile.DownloadAndSaveFileOptions{
        URL: "https://example.com/sample-image.jpg",
        SavePaths: []string{
            "/tmp/images/sample.webp",    // WebP format
            "/tmp/backup/sample.jpg",     // JPEG backup
        },
        IsPhoto:      true,
        CompressWebP: true,
        MaxRetries:   5,
    }

    results, err := gofile.DownloadAndSaveFile(opts)
    if err != nil {
        log.Fatal("Download failed:", err)
    }

    log.Printf("Successfully saved %d files:", len(results))
    for _, path := range results {
        log.Printf("  - %s", path)
    }

    // Download and resize
    img, err := gofile.DownloadAndResizeImage(
        "https://example.com/large-photo.jpg",
        1200, 800,
    )
    if err != nil {
        log.Fatal("Download and resize failed:", err)
    }

    // Save resized image
    resizedPath, err := gofile.SaveImage(img, "/tmp/images/resized.webp", true)
    if err != nil {
        log.Fatal("Save failed:", err)
    }

    log.Printf("Resized image saved to: %s", resizedPath)
}
```


